import { defineBuildConfig } from './build';

const isProd = process.env.NODE_ENV === 'production';
const isSupportOSS =
  process.env.ENABLE_OSS === 'true' && process.env.OSS_URL && process.env.OSS_PROJECT_FOLDER;

export default defineBuildConfig({
  port: 8080,
  mode: isProd ? 'ssg' : 'spa',
  css: ['@/assets/styles/main.less'],
  plugins: [
    '@/plugins/antd.plugin',
    '@/plugins/service.plugin',
    {
      src: '@/plugins/auth.plugin',
      mode: 'client',
    },
    {
      src: '@/plugins/umeng.analysis.plugin',
      mode: 'client',
    },
    {
      src: '@/plugins/tracer.plugin/nuxt',
      mode: 'client',
    },
  ],
  publicPath: isSupportOSS
    ? `${process.env.OSS_URL.replace(/(^https?:\/\/)/, '//')}/${process.env.OSS_PROJECT_FOLDER}`
    : undefined,

  /**
   * SEO
   */
  head: {
    title: '受益所有人管理平台',
    titleTemplate: '企查查 | %s',

    htmlAttrs: {
      lang: 'zh-CN',
    },
    // `hid` 是 `vue-meta` 识别 meta 标签的唯一标识, 用于在页面级别覆盖
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { name: 'format-detection', content: 'telephone=no,email=no,address=no' },
      { name: 'applicable-device', content: 'pc,mobile' },
    ],
    script: [
      {
        innerHTML: `
          window.AliyunCaptchaConfig = {
            region: "cn",
            prefix: "51cxga",
          };
        `,
      },
    ],
    __dangerouslyDisableSanitizers: ['script']
  },

  /**
   * Axios
   */

  axios: {
    // baseURL: '/',
    // credentials: true,
    proxy: true,
  },

  /**
   * Proxy
   */
  proxy: {
    '/qcc/': {
      target: process.env.API_ENTRYPOINT || 'http://kys.test.greatld.com',
      changeOrigin: true,
      xfwd: false, // 禁止本地 node 服务器重写 x-forward-for
    },
    '/benefishield': {
      target: process.env.API_ENTRYPOINT || 'http://aml.test.greatld.com',
      changeOrigin: true,
      xfwd: false,
    },
  },

  /**
   * Environment variables
   */
  env: {
    RUNTIME_ENV: process.env.SENTRY_ENVIRONMENT || 'test', // CI 运行环境变量 'dev' | 'test' | 'prod'
  },
  /**
 * Client-side config
 */
  publicRuntimeConfig: {
    /**
     * Tracer plugin config
     */
    tracer: {
      appName: 'kys-pc-web',
      applicationName: '受益人',
      // enable: process.env.SENTRY_ENVIRONMENT !== undefined, // 本地环境不开启
      enable: false, // 关闭埋点
      debug: !isProd, // 仅本地环境开启
    },
    /**
     * UMeng analysis
     */
    umeng: {
      siteId: '1281383743',
    },
  },
});
