import { Plugin } from '@nuxt/types';

const UMengAnalysisPlugin: Plugin = ({ $config, app: { router } }) => {
  if (process.env.NODE_ENV !== 'production' || !process.client || process.env.RUNTIME_ENV !== 'prod') {
    return;
  }

  // 初始化
  const SITE_ID = $config.umeng.siteId;
  const SDK_URL = `//v1.cnzz.com/z.js?id=${SITE_ID}&async=1`;

  const um = document.createElement('script');
  um.src = SDK_URL;
  const s = document.getElementsByTagName('script')[0];
  s.parentNode.insertBefore(um, s);

  // 手动埋点PV
  router.afterEach((to) => {
    try {
      window._czc = window._czc || [];
      window._czc.push(['_trackPageview', to.fullPath]);
    } catch (error) { }
  });
};

export default UMengAnalysisPlugin;

declare global {
  interface Window {
    _czc?: [string, string][];
  }
}
