import { defineComponent } from '@nuxtjs/composition-api';
import styles from './styles.module.less';
import LandingSection from '@/features/landing-section';
import LandingWrapper from '@/features/landing-wrapper';

const AboutUsPage = defineComponent({
  name: 'AboutUsPage',
  layout: 'portal',
  render() {
    return (
      <LandingSection title="关于我们">
        <LandingWrapper>
          <div class={styles.container}>
            <p>
              聚焦金融行业反洗钱合规核心需求，提供受益人识别、受益人核验、受益人差异分析与持续监控的全流程一站式解决方案，帮助金融机构在客户关系全周期中降低反洗钱合规风险，提升尽职调查效率，为风险管控提供关键数据支撑
            </p>
            <p>核心功能模块:</p>
            <p>
              <strong>受益人识别服务：</strong>
              支持通过企业名称或统一社会信用代码进行受益人识别，输出受益人权利信息，满足金融机构初步识别受益人的需求
            </p>
            <p>
              <strong>备案义务判断辅助：</strong>
              内置备案义务判断规则模型，自动判断企业是否属于备案主体，提示是否需进行受益人信息申报，提升金融机构一线人员合规判断效率
            </p>
            <p>
              <strong>差异核验与报告生成：</strong>
              提供受益人与BOMIS核验系统比对的能力，自动识别与备案信息的差异，生成差异报告与可回溯的佐证材料，满足义务机构在开户与持续客户管理中的差异报送义务
            </p>
            <p>
              <strong>受益人持续监控：</strong>
              7×24小时监测市场主体受益人变动，自动推送企业受益人变化信息，辅助金融机构实现“持续识别”要求，降低遗漏变更带来的合规风险
            </p>

            <p>
              <strong>电话咨询：</strong>400-088-8275
            </p>
            <p class={styles.address}>
              <strong>联系地址：</strong>
              <span>
                江苏省苏州市工业园区汇智街8号<i>（总部）</i>
                <br />
                上海市浦东新区世纪大道1198号世纪汇1座1001室<i>（子公司）</i>
                <br />
                北京市朝阳区望京东园四区浦项中心B座2层207室<i>（子公司）</i>
                <br />
                广东省深圳市南山区科发路19号华润置地大厦D座7楼<i>（子公司）</i>
              </span>
            </p>
          </div>
        </LandingWrapper>
      </LandingSection>
    );
  },
});

export default AboutUsPage;
