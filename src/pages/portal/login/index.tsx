import { defineComponent } from '@nuxtjs/composition-api';
import Auth from './widgets/auth';
import Wrapper from './widgets/wrapper';
import styles from './login.module.less';

const LoginPage = defineComponent({
  name: 'LoginPage',
  layout: 'auth',
  render() {
    return (
      <div class={styles.container}>
        <Wrapper>
          <div class={styles.hero}>
            <div class={styles.info}>
              <h1>受益所有人系统</h1>
              <h2>洞察风险，智慧决策</h2>
              <p>
                企查查受益所有人系统，助力构建全面的定制化风险管理框架，识别和防范潜在的多种风险，有效降低风险管理成本，让风险管理变得更加高效、智能。
              </p>
            </div>
            <Auth />
          </div>
        </Wrapper>
      </div>
    );
  },
});

export default LoginPage;
