import { Button, Input, message } from 'ant-design-vue';
import axios from 'axios';
import { computed, defineComponent, ref, shallowReactive, unref } from 'vue';
import OAuth from './oauth';
import { emailValidator, passwordValidator, phoneNumberValidator, verifyCodeValidator } from './validator';
import styles from './with-login.module.less';
import PasswordInput from '@/components/password-input';
import VerifyCodeInput from '@/components/verify-code-input';
import env from '@/config/env';
import { service } from '@/services';
import { encryptAES } from '@/utils/crypto';

const LOGIN_ACCOUNT_STORAGE_KEY = 'AML_USER_LOGIN_ACCOUNT';

enum SubmitStatus {
  Idle = 'idle',
  Pending = 'pending',
  Success = 'success',
  Error = 'error',
}

function getAccountFromStorage(key = LOGIN_ACCOUNT_STORAGE_KEY): string | null {
  if (!process.client) {
    return null;
  }
  return localStorage.getItem(key);
}

function setAccountToStorage(value: string, key = LOGIN_ACCOUNT_STORAGE_KEY): void {
  if (!process.client) {
    return null;
  }
  return localStorage.setItem(key, value);
}

type LoginType = 'password' | 'sms';

type LoginByPasswordPayload =
  | ({
      passwd: string;
      redirectUrl: string;
      fallbackUrl: string;
      /** 是否加密 */
      isEncrypted: boolean;
    } & {
      mobile: string;
    })
  | {
      email: string;
    };

type LoginBySMSPayload = {
  mobile: string;
  code: string;
  redirectUrl: string;
  fallbackUrl: string;
  /** 是否加密 */
  isEncrypted: boolean;
};

function validateEmail(email) {
  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  return emailRegex.test(email);
}

const WithLogin = defineComponent({
  name: 'WithLogin',
  setup() {
    const submitStatus = ref<SubmitStatus>(SubmitStatus.Idle);
    const loginType = ref<LoginType>('sms');
    const handleChangeLoginType = (currentLoginType: LoginType) => {
      loginType.value = currentLoginType;
    };
    const formData = shallowReactive({
      account: getAccountFromStorage() || '',
      code: '',
      password: '',
      remember: true,
    });

    const submitText = computed(() => {
      switch (unref(loginType)) {
        case 'sms':
          return '登录/注册';
        case 'password':
        default:
          return '立即登录';
      }
    });

    // 验证表单数据格式
    const validateFormData = (loginType: LoginType): boolean => {
      const account = formData.account.trim();
      if (loginType === 'password') {
        if (validateEmail(account)) {
          return emailValidator(account) && passwordValidator(formData.password);
        }
        return phoneNumberValidator(account) && passwordValidator(formData.password);
      }
      return phoneNumberValidator(account) && verifyCodeValidator(formData.code);
    };

    // 密码登陆
    const loginByPassword = (formData) => {
      let payload = {
        passwd: encryptAES(formData.password, env.AES_SECRET_KEY),
        redirectUrl: formData.redirectUrl,
        fallbackUrl: formData.fallbackUrl,
        isEncrypted: true,
      } as LoginByPasswordPayload;

      if (validateEmail(formData.account)) {
        payload = {
          ...payload,
          email: formData.account,
        };
      } else {
        payload = {
          ...payload,
          mobile: formData.account,
        };
      }
      return service.auth.loginByPassword(payload);
    };

    // 短信登录
    const loginBySMS = (formData) => {
      const payload = {
        mobile: formData.account,
        code: encryptAES(formData.code, env.AES_SECRET_KEY),
        redirectUrl: formData.redirectUrl,
        fallbackUrl: formData.fallbackUrl,
        isEncrypted: true,
      } as LoginBySMSPayload;
      return service.auth.loginBySMS(payload);
    };

    // 登录
    const loginRequest = () => {
      const redirectBaseUrl = location.origin || env.AML_HOME;
      const payload = {
        account: formData.account.trim(),
        redirectUrl: `${redirectBaseUrl}${env.AML_ENTRY}`,
        fallbackUrl: redirectBaseUrl,
        password: formData.password,
        code: formData.code,
      };
      if (loginType.value === 'password') {
        return loginByPassword(payload);
      }
      return loginBySMS(payload);
    };

    // 发送验证码
    const handleSendCode = async () => {
      if (!phoneNumberValidator(formData.account)) {
        return Promise.reject(new Error('手机号格式不正确'));
      }
      try {
        await service.auth.sendVerifyCode({
          phone: formData.account.trim(),
        });
      } catch (error) {
        let errorMessage = '发送验证码失败，请稍后再试';
        if (axios.isAxiosError(error)) {
          errorMessage = error.response.data?.error ?? '发送验证码失败，请稍后再试';
        }
        message.error(errorMessage);
        console.error(error);
      }
    };

    // 提交登录表单
    const handleSubmit = async () => {
      // 数据格式校验
      if (!validateFormData(loginType.value)) {
        return;
      }

      try {
        submitStatus.value = SubmitStatus.Pending;
        // 记录手机号到本地
        setAccountToStorage(formData.account.trim());
        // 登录
        const { data: { redirectUrl } } = await loginRequest();
        if (!redirectUrl) {
          throw new Error('跳转链接不存在');
        }
        submitStatus.value = SubmitStatus.Success;
        window.location.href = redirectUrl;
      } catch (error) {
        submitStatus.value = SubmitStatus.Error;
        let errorMessage = '登录失败，请稍后再试';
        if (axios.isAxiosError(error)) {
          errorMessage = error.response.data?.error ?? errorMessage;
        }
        message.error(errorMessage);
        console.error(error);
      }
    };

    return {
      loginType,
      handleChangeLoginType,
      submitText,
      formData,
      handleSubmit,
      handleSendCode,
      submitStatus,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.body}>
          <div class={styles.tabs}>
            <div
              class={{
                [styles.item]: true,
                [styles.active]: this.loginType === 'sms',
              }}
              onClick={() => this.handleChangeLoginType('sms')}
            >
              快捷登录
            </div>
            <div
              class={{
                [styles.item]: true,
                [styles.active]: this.loginType === 'password',
              }}
              onClick={() => this.handleChangeLoginType('password')}
            >
              密码登录
            </div>
          </div>
          {/* 密码登录 */}
          <div v-show={this.loginType === 'password'}>
            <div class={styles.row}>
              {/* 保持与封装控件结构一致 */}
              <span class={styles.input}>
                <Input v-model={this.formData.account} placeholder="请输入手机号码或电子邮箱" size="large" />
              </span>
            </div>
            <div class={styles.row}>
              <PasswordInput v-model={this.formData.password} placeholder="请输入密码" size="large" />
            </div>
          </div>
          {/* 快捷登录 */}
          <div v-show={this.loginType === 'sms'}>
            <div class={styles.row}>
              {/* 保持与封装控件结构一致 */}
              <span class={styles.input}>
                <Input v-model={this.formData.account} placeholder="请输入手机号码" size="large" />
              </span>
            </div>
            <div class={styles.row}>
              <VerifyCodeInput
                v-model={this.formData.code}
                placeholder="短信验证码"
                size="large"
                maxLength={6}
                sender={this.handleSendCode}
              />
            </div>
          </div>

          {/* <div class={styles.row}>
            <div class={styles.info}>
              <Checkbox v-model={this.formData.remember}>一周内保持登录状态</Checkbox>
              <a>忘记密码</a>
            </div>
          </div> */}

          <div class={styles.row}>
            <Button
              type="primary"
              size="large"
              block
              loading={this.submitStatus === SubmitStatus.Pending}
              onClick={this.handleSubmit}
            >
              {this.submitText}
            </Button>
          </div>
          <div class={styles.row}>
            <div class={styles.actions}>
              <nuxt-link to="/portal/reset-password" target="_self" rel="nofollow">
                忘记密码
              </nuxt-link>
              <OAuth />
            </div>
          </div>
        </div>
        <div class={styles.footer}>
          <p>
            登录即表示已阅读并同意
            <nuxt-link to="/portal/terms-of-service" target="_blank" rel="nofollow">
              《企查查受益所有人服务协议》
            </nuxt-link>
          </p>
        </div>
      </div>
    );
  },
});

export default WithLogin;
