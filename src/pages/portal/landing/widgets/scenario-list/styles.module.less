@import '@/assets/styles/token.less';

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: -10px;

  .item + .item {
    margin-left: 20px;
  }

  .item {
    flex: 1;
    border-radius: 4px;
    min-height: 200px;
    padding: 20px;
    border: 1px solid #eee;
    background: fff;
    cursor: pointer;
  }

  .icon {
    width: 45px;
    height: 45px;
    margin: 0 auto;
  }

  .title,
  .description {
    margin-top: 15px;
  }

  .title {
    color: #333;
    font-size: 20px;
    line-height: 28px;
    margin-bottom: 10px;
    font-weight: @base-font-bold;
    text-align: center;
  }

  .description {
    color: #666;
    font-size: 14px;
    line-height: 22px;
    margin-left: 30px;
    margin-right: 30px;
  }
}
