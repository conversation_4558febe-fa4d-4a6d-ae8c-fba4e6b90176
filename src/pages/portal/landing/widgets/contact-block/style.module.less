.container {
  width: 420px;
  height: 320px;
  border-radius: 4px;
  border-top: 8px solid #128bed;
  padding: 28px 20px 20px 20px;
  background: #f8fbfe;
  box-shadow: 0px 2px 4px 0px rgba(3, 103, 255, 0.2);

  &:hover {
    background: #ffffff;

    .button {
      background: #128bed;
      color: #ffffff;
      border-color: #128bed;
    }
  }

  .title {
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
    color: #333333;
  }

  .description {
    font-size: 14px;
    line-height: 22px;
    color: #999999;
  }

  .title + .description {
    margin-top: 8px;
  }

  .button {
    width: 100%;
    margin: 28px 0;

    height: 40px;
    border-radius: 2px;
    padding: 8px 16px;
    background: #ffffff;
    border: 1px solid #d8d8d8;

    font-size: 16px;
    font-weight: 500;
    color: #333333;

    &:hover {
      background: #0069bf;
      border-color: #0069bf;
    }
  }

  .info {
    li {
      font-size: 14px;
      line-height: 22px;
      color: #666666;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .icon {
      color: #128bed;
    }
  }
  li + li {
    margin-top: 8px;
  }
}
