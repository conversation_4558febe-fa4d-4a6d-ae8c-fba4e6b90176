import { Button } from 'ant-design-vue';
import { defineComponent, PropType } from 'vue';
import styles from './style.module.less'
import IconCheck from '@/assets/icons/icon-check.svg?inline'

type ContactItem = {
  key: string;
  title?: string;
  description?: string;
  buttonText?: string;
  infoList?: string[];
};

const ContactBlock = defineComponent({
  functional: true,
  props: {
    item: {
      type: Object as PropType<ContactItem>,
      required: true,
    },
  },
  render(h, { props, listeners }) {
    const { item } = props;
    return (
      <div class={styles.container}>
        <div class={styles.title}>{item.title}</div>
        <div class={styles.description}>{item.description}</div>
        <Button class={styles.button} onClick={listeners.applyTrial}>{item.buttonText || '联系我们'}</Button>
        {item.infoList?.length > 0 ? (
          <ul class={styles.info}>
            {item.infoList.map((item) => (
              <li>
                <IconCheck class={styles.icon} />
                <span>{item}</span>
              </li>
            ))}
          </ul>
        ) : null}
      </div>
    );
  },
});

export default ContactBlock;
