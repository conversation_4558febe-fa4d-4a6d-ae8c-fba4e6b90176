import { defineComponent, type PropType } from 'vue';
import styles from './styles.module.less';

type CustomerListItem = {
  logo: string;
  intro: string;
};

const CustomerList = defineComponent({
  functional: true,
  props: {
    items: {
      type: Array as PropType<Array<CustomerListItem> | ReadonlyArray<CustomerListItem>>,
      required: true,
    },
  },
  render(h, { props }) {
    return (
      <div class={styles.container}>
        {props.items.map((item, index) => {
          return (
            <div class={styles.item} key={index}>
              <div class={styles.logo}>
                <img src={item.logo} />
              </div>
              <p class={styles.intro}>{item.intro}</p>
            </div>
          );
        })}
      </div>
    );
  },
});

export default CustomerList;
