import { PropType, defineComponent } from 'vue';
import { Button } from 'ant-design-vue';
import SectionBlock from '../section-block';
import styles from './styles.module.less';
import Carousel from '@/components/carousel';

type DataSource = {
  name: string;
  description?: string;
  decorator?: string; // 修饰图
};

type ItemProp = {
  key: string;
  title?: string;
  description?: string;
  link?: string;
  dataSource: DataSource[];
};

const CarouselBlock = defineComponent({
  functional: true,
  props: {
    item: {
      type: Object as PropType<ItemProp>,
      required: true,
    },
  },
  render(h, { props, data, listeners }) {
    const { item } = props;
    return (
      <SectionBlock {...data} title={item.title} description={item.description}>
        <div class={styles.container}>
          <Carousel items={item.dataSource}>
            <Button type="primary" onClick={listeners.applyTrial}>申请试用</Button>
          </Carousel>
        </div>
      </SectionBlock>
    );
  },
});

export default CarouselBlock;
