@import '@/assets/styles/token.less';

.container {
  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 68px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      #e5efff 31%,
      #e5efff 69%,
      rgba(255, 255, 255, 0) 100%
    );
    h3 {
      font-size: 30px;
      font-weight: 700;
      line-height: 38px;
      color: @base-color-black-800;
    }
    span {
      color: @base-color-black-400;
      font-size: 22px;
    }
    > *:not(:last-child) {
      margin-right: 20px;
    }
  }

  .content {
    padding: 72px;
    .item {
      font-size: 26px;
      line-height: 1;
      display: flex;
      align-items: center;
      &:not(:last-child) {
        margin-bottom: 40px;
      }
      b {
        color: #0266ff;
        font-family: 'Digit', sans-serif;
        margin-right: 10px;
        font-size: 32px;
        transform: translateY(4px);
      }
      i {
        margin-left: 20px;
        color: @base-color-black-400;
        font-size: 20px;
      }
      b,
      strong {
        font-weight: 700;
      }
    }
  }
}
