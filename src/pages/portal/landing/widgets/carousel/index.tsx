import { useContext } from '@nuxtjs/composition-api';
import { defineComponent, ref, unref, PropType, computed } from 'vue';
import styles from './styles.module.less';
import { useTracer } from '@/composables/use-tracer';

type SubContent = {
  title:string;
  content:string;
}

type CarouselItem = {
  name: string;
  icon?: string;
  description?: string;
  sub?:SubContent[];
  decorator?: string; // 修饰图
};

const Carousel = defineComponent({
  name: 'Carousel',
  props: {
    items: {
      type: Array as PropType<CarouselItem[]>,
      required: true,
    },
  },
  setup(props) {
    const { route } = useContext();
    const { clickEvent } = useTracer(route);

    const activeKey = ref(0);
    const changeActiveKey = (key) => {
      activeKey.value = key;
      clickEvent(activeItem.value.name)
    };
    const activeItem = computed(() => {
      return props.items[unref(activeKey)];
    });
    return {
      activeKey,
      changeActiveKey,
      activeItem,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.tabs}>
          {this.items.map(({ icon, name }, index) => {
            return (
              <div
                class={{
                  [styles.item]: true,
                  [styles.active]: index === this.activeKey,
                }}
                key={`tab-${index}`}
              >
                <div class={styles.tab} onClick={() => this.changeActiveKey(index)}>
                  <span
                    class={styles.icon}
                    domPropsInnerHTML={require(`@/assets/icons/${icon}.svg?raw`)}
                  ></span>
                  <span class={styles.title}>{name}</span>
                </div>
              </div>
            );
          })}
        </div>
        <div class={styles.content}>
          <main class={styles.main}>
            <div class={styles.title}>
              <span>{this.activeItem.name}</span>
            </div>
            {this.activeItem.description && <div class={styles.description}>{this.activeItem.description}</div>}
            {this.activeItem.sub && this.activeItem.sub.map(item => {
              return (
                <p class={[styles.description, styles.sub]}><span class={styles.emphasis}>{item.title}</span>{item.content}</p>
              )
            })}
          </main>
          <aside class={styles.aside}>
            <img width="580" src={this.activeItem.decorator} alt={this.activeItem.name} />
          </aside>
        </div>
      </div>
    );
  },
});

export default Carousel;
