import { defineComponent } from 'vue';
import styles from './styles.module.less';

const SectionBlock = defineComponent({
  name: 'SectionBlock',
  props: {
    title: {
      type: String,
      required: false,
    },
    description: {
      type: String,
      required: false,
    },
    background: {
      type: String,
      required: false,
    },
    bodyStyle: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const { $slots, background, title, description, bodyStyle } = this;

    return (
      <section
        class={styles.container}
        style={{
          background: background ? `url(${background}) 50% 50% / cover no-repeat` : undefined,
        }}
      >
        {title || description ? (
          <header class={styles.header}>
            <div class={styles.title}>{title}</div>
            {description ? <div class={styles.description} domPropsInnerHTML={description}></div> : null}
          </header>
        ) : null}
        <div
          class={styles.body}
          style={{
            width: '1200px',
            ...bodyStyle,
          }}
        >
          {$slots.default}
          {$slots.more ? <div class={styles.more}>{$slots.more}</div> : null}
        </div>
      </section>
    );
  },
});

export default SectionBlock;
