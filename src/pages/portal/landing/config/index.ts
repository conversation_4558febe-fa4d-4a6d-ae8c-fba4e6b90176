// import LogoZheshang from '../assets/customers/logo-zheshang.png';
// import LogoStateGrid from '../assets/customers/logo-state-grid.png';
// import LogoSinoGrain from '../assets/customers/logo-sino-grain.png';
// import LogoSiemens from '../assets/customers/logo-siemens.png';

// import Scenario1 from '../assets/scenarios/scenario-1.png';
// import Scenario2 from '../assets/scenarios/scenario-2.png';
// import Scenario3 from '../assets/scenarios/scenario-3.png';

// import CaseStudy12 from '../assets/case-study/case-study-1-2.jpg';
// import CaseStudy13 from '../assets/case-study/case-study-1-3.jpg';
// import CaseStudy14 from '../assets/case-study/case-study-1-4.jpg';
// import CaseStudy15 from '../assets/case-study/case-study-1-5.jpg';
// import CaseStudy21 from '../assets/case-study/case-study-2-1.jpg';
// import CaseStudy22 from '../assets/case-study/case-study-2-2.jpg';
// import CaseStudy23 from '../assets/case-study/case-study-2-3.jpg';
// import CaseStudy24 from '../assets/case-study/case-study-2-4.jpg';
// import CaseStudy31 from '../assets/case-study/case-study-3-1.jpg';
// import CaseStudy32 from '../assets/case-study/case-study-3-2.jpg';
// import CaseStudy33 from '../assets/case-study/case-study-3-3.jpg';
import CaseSolution1 from '../assets/case-study/case-solution-1.png';
import CaseSolution2 from '../assets/case-study/case-solution-2.png';
import CaseSolution3 from '../assets/case-study/case-solution-3.png';
import CaseSolution4 from '../assets/case-study/case-solution-4.png';

/**
 * 三大场景
 */
/* export const SCENARIO_LIST = Object.freeze([
  {
    key: 'supplier-risk-management',
    name: '全方位风险监控',
    description: '法律和监管变化跟踪、市场风险评估、信用风险管理、合规性检查',
    icon: Scenario1,
  },
  {
    key: 'purchase-contract-risk-management',
    name: '企业合规内控管理',
    description: '合作准入排查、利益冲突排查、深度关系挖掘',
    icon: Scenario2,
  },
  {
    key: 'supplier-compliance-risk-management',
    name: '客户尽职调查',
    description: '经营风险排查、关联方风险排查、主要人员风险排查、未来风险预测',
    icon: Scenario3,
  },
]); */

/**
 * 使用场景
 */
/* export const CASE_STUDY_LIST = Object.freeze([
  [
    // {
    //   name: '法律和监管变化跟踪',
    //   icon: 'icon-zhunrufengxianpaicha',
    //   sub: [
    //     {
    //       title: '潜在风险识别：',
    //       content:
    //         '在供应商准入环节，我们进行全面的资质综合排查，以高效快速地识别潜在的风险，如行贿犯罪、商业贿赂、内部腐败、潜在利益冲突等。这有助于排除存在明显问题的企业，从源头预防潜在风险的发生。',
    //     },
    //     {
    //       title: '查查信用分：',
    //       content:
    //         '通过机器学习算法及大数据模型，从企业背景、经营状况、资质评价、履约历史、管理水平等多维度给企业信用定量打分，辅助风险评估。',
    //     },
    //   ],
    //   decorator: CaseStudy11,
    // },
    {
      name: '法律和监管变化跟踪',
      icon: 'icon-liyichongtupaicha',
      description: '系统跟踪相关法律和监管变化，确保公司及时调整政策和流程以保持合规',
      decorator: CaseStudy12,
    },
    {
      name: '市场风险评估',
      icon: 'icon-heimingdanguanli',
      description: '通过分析市场趋势和相关指标，系统评估市场风险对公司业务的潜在影响',
      decorator: CaseStudy13,
    },
    {
      name: '信用风险管理',
      icon: 'icon-zhunrupaicha',
      description:
        '对企业客户信用评估相关指标进行监控和跟踪，便于及时调整企业客户的信用额度，确保交易在风险可控范围内进行',
      decorator: CaseStudy14,
    },
    {
      name: '合规性检查',
      icon: 'icon-chixujintiao2',
      description: '跟踪法规要求的多项指标，在指标发生变化时即时产生动态并推送，确保及时响应合规检查',
      decorator: CaseStudy15,
    },
  ],
  // 客户尽职调查
  [
    {
      name: '经营风险',
      icon: 'icon-lishitoubiaofenxi',
      description:
        '通过分析企业的财务报表、市场趋势、竞争对手动态和客户反馈，帮助识别和预测可能影响企业日常运营的潜在风险。',
      decorator: CaseStudy21,
    },
    {
      name: '关联方风险',
      icon: 'icon-yisiguanxiwajue',
      description:
        '通过对不同主体之间之间存在的疑似相同电话、相同邮箱、相同知识产权、相同案件信息进行分析，深度挖掘企业间存在的潜在关联关系与风险。',
      decorator: CaseStudy22,
    },
    {
      name: '主要人员风险',
      icon: 'icon-touzirenzhiguanxifaxian',
      description:
        '关注企业关键人员如高级管理人员、创始人或关键技术人员的行为和背景，通过分析这些人员的历史信用记录、法律诉讼、职业道德和社交媒体活动，评估其可能对企业带来的风险。',
      decorator: CaseStudy23,
    },
    {
      name: '未来风险预测',
      icon: 'icon-lishiweichuanbiaobuliangjilu',
      description:
        '利用先进的数据分析和机器学习模型，预测企业在未来可能面临的风险，包括宏观经济波动、行业趋势变化、技术创新的影响以及潜在的市场机会。',
      decorator: CaseStudy24,
    },
  ],
  // 企业合规内控管理
  [
    {
      name: '合作准入排查',
      icon: 'icon-hezuozhunrupaicha',
      description:
        '对第三方基本信息、法律诉讼、行政监管、经营稳定性等多维度风险事项进行排查，结合与合作伙伴、内部员工、内外黑名单数据等多类主体间的关联关系排查，有效降低企业第三方合作伙伴可能存在的行贿犯罪、商业贿赂、内部腐败、潜在利益冲突等风险，帮助企业排除有明显问题的合作方，防范风险于未然。',
      decorator: CaseStudy31,
    },
    {
      name: '利益冲突排查',
      icon: 'icon-liyichongtupaicha',
      description:
        '对企业内部高管、员工、员工近亲属、曾被处罚的现任员工或前员工与第三方企业进行任职、对外投资排查，识别员工是否在相关供应商担任合作伙伴的股东及董监高法等，规避相关利益冲突风险。',
      decorator: CaseStudy32,
    },
    {
      name: '深度关系挖掘',
      icon: 'icon-shenduguanxiwajue',
      description:
        '深度挖掘内部股东高管与已准入或合作中供应商之间的深层次关系网络，避免商业贿赂、利益输送等风险。',
      decorator: CaseStudy33,
    },
  ],
]); */

/* export const CUSTOMER_LIST = Object.freeze([
  {
    logo: LogoZheshang,
    intro:
      '企查查专业版为中拓CRM系统提供了完善而丰富客户数据，从多个数据维度，丰富客户画像，加强客户风险量化管理，从而增强客户风险识别能力，并积累风控数据。',
  },
  {
    logo: LogoStateGrid,
    intro:
      '企查查专业版为国网提供了基于企业信用、市场信用、行业信用的信用评价模型，多维度信用评价形成客观、公正、全面、直观的信用风险评价结果，助力国网搭建全域风控预警监测系统。',
  },
  {
    logo: LogoSinoGrain,
    intro:
      '企查查专业版为中储粮网电商平台提供企业工商信息，企业经营信息，关键人风险，舆情信息，历史数据，监控服务等信息，实现会员信息化管理，做到客户风险管理的事前关注、事中控制。',
  },
  {
    logo: LogoSiemens,
    intro: '企查查专业版为西门子提供经销商背调解决方案，为西门子构建经销商风险评估体系，助力企业合规发展。',
  },
]); */

export const SOLUTION_DATA = {
  key: 'solution',
  dataSource: [
    {
      name: '受益所有人识别',
      description:
        '遵循法律法规，全面穿透“拥有、控制、收益”关系，锁定最终受益人，覆盖直接与间接持股等复杂场景。支持单一或批量主体识别，快速输出清晰的识别结果与依据，内置备案规则模型，自动判断是否需备案，助力一线人员高效合规决策。',
      decorator: CaseSolution1,
    },
    {
      name: '受益所有人核验',
      description:
        '无缝对接人民银行 BOMIS 系统，自动化完成义务机构信息与官方备案信息的比对核验，一键识别 “一致、不一致、主体未备案” 三种结果。替代传统人工核对模式，大幅降低人为误差，确保核验过程合规可追溯，助力金融机构高效满足监管要求。',
      decorator: CaseSolution2,
    },
    {
      name: '受益所有人差异分析',
      description:
        '针对核验不一致的情况，自动生成标准化差异报告，详细分析受益所有人多出、缺失及信息不一致等问题。同步提供基于公开信息的佐证材料，清晰还原判断逻辑，帮助金融机构快速定位差异根源，高效完成差异上报。',
      decorator: CaseSolution3,
    },
    {
      name: '受益所有人持续监控',
      description:
        '7×24 小时实时监测市场主体受益所有人信息变动，涵盖股权比例、受益权期限、关键任职等维度，推送变更前后对比数据。助力金融机构在客户关系存续期间实现动态跟踪，及时响应 “持续识别” 监管要求，有效降低因信息遗漏导致的合规风险。',
      decorator: CaseSolution4,
    },
  ],
};

export const CONTACT_BLOCK_DATA = [
  {
    key: 'local',
    title: '本地化解决方案',
    description: '开箱即用的一站式解决方案',
    infoList: ['受益所有人识别', '受益所有人核验', '受益所有人差异分析', '受益所有人持续监控'],
  },
  {
    key: 'saas',
    title: 'SaaS解决方案',
    description: '标准产品输出，支持业务接口',
    infoList: ['受益所有人识别', '受益所有人佐证材料', '受益所有人持续监控'],
  },
];
