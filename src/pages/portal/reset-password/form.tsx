import { get } from 'lodash';
import { defineComponent, ref } from 'vue';
import { Form, Input, message } from 'ant-design-vue';
import NoCaptcha from '@/components/no-captcha';
import CountDown from '@/components/count-down';
import { service } from '@/services';
import { useNoCaptcha } from '@/components/no-captcha/use-no-captcha';
import PasswordInput from '@/components/password-input';
import { emailReg, phoneReg } from '@/utils/pattern';

const ResetPasswordForm = defineComponent({
  name: 'ResetPasswordForm',
  props: {
    form: {
      type: Object,
      required: false,
    },
  },
  setup(props) {
    const [verifyToken, isCaptchaValid, handleCaptchaSuccess] = useNoCaptcha();
    const noCaptchaRef = ref();

    /**
     * 发送验证码
     */
    const handleSendVerifyCode = async (): Promise<boolean> => {
      const { phone } = props.form.getFieldsValue();
      if (!phone) {
        message.warning('请输入正确的手机号码或电子邮箱');
        return false;
      }
      if (!verifyToken.value) {
        message.warning('请拖动滑块进行人机验证');
        return false;
      }
      const key = emailReg.test(phone) ? 'email' : 'phone';
      try {
        const {data}: any = await service.user.sendResetPasswordVerifyCodeBySMS({
          [key]: phone,
          token: verifyToken.value,
        });

        // 有验证码的情况下才需要去比对code
        if (!data?.[key]) {
          message.error('验证码发送失败，请重试');
          return;
        }
        message.success('验证码发送成功');
        return true;
      } catch (error) {
        const errInfo = get(error, 'response.data.error') || '验证码发送失败，请重试'
        message.error(errInfo);
        const code = get(error, 'response.data.code');
        if (code === 200502) {
          noCaptchaRef.value?.reset?.();
        }
        return false;
      }
    };

    return {
      isCaptchaValid,
      handleCaptchaSuccess,
      handleSendVerifyCode,
    };
  },
  render() {
    return (
      <Form form={this.form} colon={false} layout="horizontal">
        <Form.Item required>
          <Input
            placeholder="请输入手机号码或电子邮箱"
            size="large"
            allowClear
            maxLength={100}
            autoComplete="new-password"
            v-decorator={[
              'phone',
              {
                rules: [
                  {
                    required: true,
                    validator: (rule, value, callback) => {
                      if (phoneReg.test(value)) {
                        callback();
                      } else if (emailReg.test(value)) {
                        callback();
                      } else {
                        callback('请输入正确的手机号码或电子邮箱');
                      }
                    },
                  },
                ],
              },
            ]}
          />
        </Form.Item>
        {/* noCaptcha */}
        <Form.Item>
          <NoCaptcha
            id="reset-password-no-captcha"
            ref="noCaptchaRef"
            sceneId="1432ga7w"
            size="lg"
            onSuccess={(data) => {
              this.handleCaptchaSuccess(data);
              this.form.setFieldsValue({ code: undefined });
            }}
          />
        </Form.Item>
        <Form.Item>
          <Input
            placeholder="请输入验证码"
            size="large"
            allowClear
            maxLength={6}
            v-decorator={[
              'code',
              {
                rules: [
                  {
                    required: true,
                    pattern: /^\d{6}$/,
                    message: '请输入验证码',
                  },
                ],
              },
            ]}
          >
            <CountDown
              style={{ width: '130px' }}
              slot="addonAfter"
              placeholder="获取验证码"
              disabled={!this.isCaptchaValid}
              action={this.handleSendVerifyCode}
            />
          </Input>
        </Form.Item>
        <Form.Item>
          <PasswordInput
            autocomplete="new-password"
            size="large"
            placeholder="请输入新密码"
            maxLength={18}
            visibilityToggle
            v-decorator={[
              'password',
              {
                rules: [
                  {
                    required: true,
                    validator: (rule, value, handler) => {
                      if (value === undefined || value.length < 8) {
                        handler('密码必须至少有8个字符');
                      } else if (value.length > 18) {
                        handler('密码不能超过18个字符');
                      } else if (!/[A-Z]/.test(value)) {
                        handler('密码至少包含一个大写字母');
                      } else if (!/[a-z]/.test(value)) {
                        handler('密码至少包含一个小写字母');
                      } else if (!/[0-9]/.test(value)) {
                        handler('密码至少包含一个数字');
                      } else if (!/[~!@#$%^&*()\-=_+{}[\]<>,.?/\\]/.test(value)) {
                        handler('密码至少包含一个特殊符号');
                      } else {
                        handler();
                      }
                    },
                  },
                ],
              },
            ]}
          />
        </Form.Item>
        <Form.Item extra="注：密码由8-18位大小写字母+数字+特殊符号组成">
          <PasswordInput
            autocomplete="new-password"
            size="large"
            placeholder="请再次输入新密码"
            maxLength={18}
            visibilityToggle
            v-decorator={[
              'confirmPassword',
              {
                rules: [
                  {
                    required: true,
                    validator: (rule, value, handler) => {
                      const password = this.form.getFieldValue('password'); // 密码
                      if (password !== value) {
                        handler('两次密码不一致');
                      } else {
                        handler();
                      }
                    },
                  },
                ],
              },
            ]}
          />
        </Form.Item>
      </Form>
    );
  },
});

export default Form.create({})(ResetPasswordForm);
