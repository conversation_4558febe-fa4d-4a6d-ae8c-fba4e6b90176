import { defineComponent, useContext } from '@nuxtjs/composition-api';
import { useToggle } from '@vueuse/core';
import styles from './portal/landing/styles.module.less';
import CarouselBlock from './portal/landing/widgets/carousel-block';
import { CONTACT_BLOCK_DATA, SOLUTION_DATA } from './portal/landing/config';
import ContactBlock from './portal/landing/widgets/contact-block';
import Section from '@/features/landing-section';
import Wrapper from '@/features/landing-wrapper';
import HeroBlock from '@/layouts/widgets/hero-block';
import { useTracer } from '@/composables/use-tracer';

const HERO_DATA = {
  title: '受益所有人管理平台',
  description: `<span>识别</span><i>|</i><span>核验</span><i>|</i><span>监控</span>`,
  background: require('../assets/images/background/wave.jpg'),
};

const LandingPage = defineComponent({
  name: 'LandingPage',
  layout: 'portal',
  setup() {
    const { route } = useContext();

    const [applyTrialVisible, setApplyTrialVisible] = useToggle(false);
    const { clickEvent } = useTracer(route);

    /**
     * 点击试用申请
     */
    const handleApplyTrial = () => {
      // setApplyTrialVisible();
      clickEvent('申请试用');
    };

    return {
      handleApplyTrial,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <HeroBlock value={HERO_DATA} background={HERO_DATA.background} />

        <Section
          title="深化业务协同"
          description="企查查与银丰新融达成战略合作，携手共创一体化受益所有人管理平台。通过构建“数据＋业务+流程”多元驱动机制，聚焦“识别＋核验＋监控”关键业务场景，实现全链路数据可视化、全过程管理规范化，创新受益所有人管理新范式。"
          theme="default"
          background={require('./portal/landing/assets/section/section-bg-blocks.jpg')}
        >
          <Wrapper>
            <div class={[styles.box, styles.gray]}>
              <img
                width="100%"
                src={require('./portal/landing/assets/collaborative-work/collaborative-work-1.png')}
                alt="深化业务协同"
              />
            </div>
          </Wrapper>
        </Section>

        <Section
          title="解决方案"
          description="企查查聚焦金融行业反洗钱合规核心需求，提供受益所有人识别、核验、差异分析与持续监控的一站式解决方案，帮助金融机构在客户关系全周期中降低反洗钱合规风险，提升尽职调查效率，为风险管控提供关键数据支撑"
          theme="blue"
          id="supplier-compliance-specific-risk-assessment"
        >
          <Wrapper>
            <CarouselBlock item={SOLUTION_DATA} onApplyTrial={this.handleApplyTrial} />
          </Wrapper>
        </Section>

        <Section
          title="选择适合您的方式"
          theme="default"
          background={require('./portal/landing/assets/section/section-bg-blocks.jpg')}
        >
          <Wrapper>
            <div class={styles.flexWrapper}>
              {CONTACT_BLOCK_DATA.map((item) => {
                return <ContactBlock item={item} onApplyTrial={this.handleApplyTrial} />;
              })}
            </div>
          </Wrapper>
        </Section>
      </div>
    );
  },
});

export default LandingPage;
