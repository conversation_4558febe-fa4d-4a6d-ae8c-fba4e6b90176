@import '@/assets/styles/token.less';

.container {
  padding: 60px 0;
  background-repeat: no-repeat;
  background-position: 50% 50%;
  background-size: cover;

  .header {
    color: @base-color-black-600;
    font-size: @base-text-md;
    text-align: center;
    margin-bottom: 50px;
  }

  .title {
    position: relative;
    padding: 0 80px;
    font-size: 30px;
    height: 39px;
    line-height: 39px;
    display: inline-block;
    font-weight: @base-font-bold;
    margin: 0;

    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 0;
      width: 80px;
      height: 39px;
      background-position: center center;
      background-repeat: no-repeat;
      background-size: 60px 20px;
    }

    &::before {
      left: 0;
      background-image: url('./assets/bg-section-header-wl.svg');
    }

    &::after {
      right: 0;
      background-image: url('./assets/bg-section-header-wr.svg');
    }
  }

  .description {
    margin: 10px auto 0;
    color: @base-color-black-300;
    width: 1200px;
    font-size: 18px;
  }

  &.default {
    background: @base-color-white;
  }

  &.gray {
    background: #f7f7f7;
    // background-image: url('./assets/bg-section-content-gray.png');
  }

  &.blue {
    background: #F2F8FE;
    // background-image: url('./assets/bg-section-content-blue.png');
  }

  &.cloudy {
    background-image: url('./assets/bg-section-content-cloudy.png');
  }
}
