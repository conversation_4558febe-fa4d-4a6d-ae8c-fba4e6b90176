import { useRouter } from '@nuxtjs/composition-api';
import { defineComponent, PropType } from 'vue';
import Logo from './images/benefishieldLogo.svg?inline';
import styles from './styles.module.less';
import env from '@/config/env';

const LandingHeader = defineComponent({
  name: 'LandingHeader',
  props: {
    theme: {
      type: String as PropType<'dark' | 'light'>,
      default: 'dark',
    },
    user: {
      type: Object as PropType<{ [key in 'id' | 'userId' | 'name' | 'currentOrg']: unknown } | undefined>,
      required: false,
    },
    /** 是否显示操作按钮 */
    showActions: {
      type: Boolean,
      default: true,
    },
    /** 固定顶部 */
    isFixed: {
      type: Boolean,
      default: true,
    }
  },
  emtis: ['apply'],
  setup(props, { emit }) {
    const router = useRouter();
    const handleRedirect = (path: string, redirect?:string) => {
      const urlObj = router.resolve({ path, query: { redirect } });
      window.location.href = urlObj.href;
    };
    const handleApply = () => {
      emit('apply');
    };
    return {
      handleRedirect,
      handleApply,
    };
  },
  render() {
    const {showActions, user} = this;
    return (
      <div class={{
        [styles.container]: true,
        [styles[this.theme]]: true,
        [styles.fixed]: this.isFixed,
      }}>
        <nuxt-link to="/" class={styles.brand}>
          <Logo />
        </nuxt-link>

        <div class={styles.menu} v-show={showActions && (user?.id || user?.userId)}>
          <div class={styles.user}>
            <span>{user?.name}</span>
            <a
              onClick={() => this.handleRedirect(env.SWITCH_ORG, '/app')}
              class={styles.link}
              v-show={user?.currentOrg}
              rel="nofollow"
            >
              切换组织
            </a>
            <a onClick={() => this.handleRedirect(env.LOGOUT)} class={styles.link} rel="nofollow">
              退出登录
            </a>
          </div>
        </div>

        <div class={styles.menu} v-show={showActions && !user}>
          <button
            onClick={() => this.handleRedirect('/portal/login')}
            class={{
              [styles.button]: true,
              [styles.default]: true,
            }}
          >
            登录
          </button>
          <button
            onClick={this.handleApply}
            class={{
              [styles.button]: true,
              [styles.primary]: true,
            }}
          >
            申请试用
          </button>
        </div>
      </div>
    );
  },
});

export default LandingHeader;
