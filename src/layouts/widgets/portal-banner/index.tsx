import { defineComponent, PropType } from 'vue';
import { isNumber } from 'lodash';
import styles from './styles.module.less';
import LandingWrapper from '@/features/landing-wrapper';
import env from '@/config/env';

const PortalBanner = defineComponent({
  functional: true,
  props: {
    user: {
      type: Object as PropType<{ [key in 'id' | 'userId' | 'name' | 'bundleError']: any } | undefined>,
      required: false,
    },
  },
  render(h, { props, listeners }) {
    const hasPermission = isNumber(props.user?.userId) && ![200008].includes(props.user?.bundleError?.code);

    const handleApply = () => {
      if (typeof listeners.apply === 'function') {
        listeners.apply();
      }
    };
    const handleIntro = () => {
      if (typeof listeners.intro === 'function') {
        listeners.intro();
      }
    };
    const handleEnter = () => {
      window.location.href = env.AML_ENTRY;
    };
    return (
      <div class={styles.container}>
        <LandingWrapper>
          <h2 class={styles.title}>受益所有人管理平台</h2>
          <h3 class={styles.title}>风险及时识别 识别准确可靠 核验充分有效 监控清晰透明</h3>
          <p class={styles.description}>企查查聚焦金融行业反洗钱合规核心需求，为金融机构提供受益所有人识别、核验、差异分析与持续监控的全流程一站式解决方案，以技术创新驱动价值增长，为用户持续创造业务价值。</p>

          <div>
            {/* <button onClick={handleIntro} class={[styles.button, styles.default]}>
              系统介绍视频
            </button> */}
            <button v-show={!hasPermission} onClick={handleApply} class={[styles.button, styles.primary]}>
              申请试用
            </button>
            <button v-show={hasPermission} onClick={handleEnter} class={[styles.button, styles.primary]}>
              进入产品
            </button>
          </div>
        </LandingWrapper>
      </div>
    );
  },
});
PortalBanner.emits = ['apply', 'intro'];

export default PortalBanner;
