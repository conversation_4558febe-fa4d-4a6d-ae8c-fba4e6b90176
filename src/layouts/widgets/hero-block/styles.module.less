.container {
  height: 200px;
  background-repeat: no-repeat;
  background-position: 50% 50%;
  background-size: cover;
  display: flex;
  justify-content: center;

  .wrapper {
    width: 886px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .main {
    text-align: center;
    .title {
      font-size: 52px;
      line-height: 70px;
      color: #000;
    }
    .description {
      margin-top: 20px;
      color: #333;
      font-size: 22px;
      line-height: 34px;
      i {
        color: #bbb;
        margin: 0 15px;
        font-family: arial, helvetica, sans-serif;
        vertical-align: bottom;
      }
      p:not(:last-child) {
        margin-bottom: 10px;
      }
    }
    .actions {
      margin-top: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      > *:not(:last-child) {
        margin-right: 20px;
      }
    }
  }
}
