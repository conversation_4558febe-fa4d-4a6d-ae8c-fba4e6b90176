import { PropType, defineComponent } from 'vue';
import styles from './styles.module.less';

export type HeroBlockProp = {
  // key: string;
  title: string;
  description: string;
  video?: string;
  href?: string;

  background?: string;
  applyTrial?: string; // 试用申请按钮文案
};

const HeroBlock = defineComponent({
  name: 'Hero<PERSON><PERSON>',
  props: {
    value: {
      type: Object as PropType<HeroBlockProp>,
      default: () => ({}),
    },
    /**
     * 背景图片
     */
    background: {
      type: String,
      required: false,
    },
  },
  emits: ['click'],
  setup(props, { emit }) {
    const handleClick = (key: string, value?: string) => {
      emit('click', key, value);
    };

    return {
      handleClick,
    };
  },
  render() {
    const {
      background,
      value: { title, description, href, video },
    } = this;
    return (
      <div
        class={styles.container}
        style={{
          backgroundImage: background ? `url(${background})` : undefined,
        }}
      >
        <div class={styles.wrapper}>
          <main class={styles.main}>
            <h1 class={styles.title}>{title}</h1>
            <div class={styles.description} domPropsInnerHTML={description} />
          </main>
        </div>
      </div>
    );
  },
});

export default HeroBlock;
