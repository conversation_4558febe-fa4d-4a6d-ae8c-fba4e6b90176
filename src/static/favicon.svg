<svg xmlns="http://www.w3.org/2000/svg" width="114" height="114" fill="none">
  <defs>
    <linearGradient id="b" x1="0" x2="1" y1=".5" y2=".5">
      <stop offset="0%" stop-color="#0266FF" />
      <stop offset="48.241%" stop-color="#0080FF" />
      <stop offset="100%" stop-color="#01A7FC" />
    </linearGradient>
    <mask id="a">
      <path fill="#FFF" fill-rule="evenodd"
        d="M29.229 0c-10.164 0-13.85 1.058-17.565 3.045-3.715 1.987-6.632 4.903-8.619 8.619C1.06 15.38 0 19.064 0 29.228v55.543c0 10.164 1.059 13.85 3.045 17.565 1.987 3.716 4.904 6.632 8.619 8.619C15.38 112.941 19.065 114 29.229 114h55.543c10.163 0 13.848-1.059 17.564-3.045 3.716-1.987 6.632-4.903 8.619-8.619S114 94.935 114 84.771V29.228c0-10.163-1.058-13.848-3.045-17.564-1.987-3.716-4.903-6.632-8.619-8.619S94.935 0 84.772 0H29.228Z"
        style="mix-blend-mode:passthrough" />
    </mask>
  </defs>
  <g style="mix-blend-mode:passthrough">
    <g mask="url(#a)" style="mix-blend-mode:passthrough">
      <path fill="url(#b)" fill-rule="evenodd" d="M-.424 114.422h115.41V-.422H-.424v114.844Z"
        style="mix-blend-mode:passthrough" />
    </g>
    <path fill="#FFF" fill-rule="evenodd"
      d="M27.21 86.79c-16.454-16.452-16.454-43.127 0-59.58 16.453-16.454 43.128-16.454 59.582 0 14.613 14.613 16.246 37.29 4.9 53.707l-5.507-5.506c8.43-13.337 6.843-31.188-4.783-42.813-13.476-13.476-35.327-13.476-48.803 0-13.477 13.477-13.477 35.327 0 48.804 13.476 13.477 35.327 13.477 48.803 0l1.232 1.232 4.158 4.157 6.017 6.018-5.234 5.234-6.477-6.476c-16.43 11.483-39.221 9.891-53.889-4.776Zm43.33-16.146c-7.513 7.513-19.692 7.513-27.205 0-7.512-7.512-7.512-19.691 0-27.204 7.513-7.512 19.692-7.512 27.205 0l.01.011 5.44-5.44c-10.503-10.502-27.53-10.502-38.031 0-10.502 10.502-10.502 27.529 0 38.03 10.493 10.494 27.5 10.501 38.003.026l-5.423-5.423Zm-10.747-10.8a4 4 0 1 1-.021-5.676l5.388-5.387c-4.54-4.517-11.881-4.512-16.412.02-4.537 4.536-4.537 11.893 0 16.43 4.537 4.537 11.894 4.537 16.432 0l-5.387-5.386Z"
      style="mix-blend-mode:passthrough" />
  </g>
</svg>