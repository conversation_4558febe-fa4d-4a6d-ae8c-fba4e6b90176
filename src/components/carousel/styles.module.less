@import '@/assets/styles/token.less';

.container {
  .tabs {
    margin-bottom: 50px;
    display: flex;
    justify-content: center;
    .wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      border-radius: 4px;
      border: 1px solid #0266ff;
    }
    .tab {
      font-size: 16px;
      padding: 12px 0;
      color: #0266ff;
      cursor: pointer;
      span {
        padding: 0 20px;
        position: relative;
        display: block;
        line-height: 16px;

        &::after {
          visibility: visible;
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          content: '';
          width: 1px;
          background-color: currentColor;
        }
      }
      &:hover {
        background: #e5efff;
      }
      &.active {
        background: #0266ff;
        color: #fff;
        span::after {
          visibility: hidden;
        }
      }
      &.active + .tab,
      &:first-child {
        span::after {
          visibility: hidden;
        }
      }
    }
  }
  .content {
    border-radius: 4px;
    display: flex;
    padding-bottom: 20px;
    .main {
      flex: 1;
      padding-right: 70px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      .title {
        line-height: 32px;
        font-size: 26px;
        font-weight: 700;
        color: @base-color-black-800;
        margin-bottom: 15px;
      }
      .description {
        font-size: 16px;
        line-height: 26px;
        color: @base-color-black-400;
      }

      .sub:not(:last-child) {
        margin-bottom: 10px;
      }

      .emphasis {
        font-weight: 500;
        color: @base-color-black-600;
      }

      .actions {
        margin-top: 40px;
        > *:not(:last-child) {
          margin-right: 15px;
        }
      }
    }
  }
}
