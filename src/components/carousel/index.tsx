import { defineComponent, ref, unref, PropType, computed } from 'vue';
import styles from './styles.module.less';

type SubContent = {
  title: string;
  content: string;
};

type CarouselItem = {
  name: string;
  description?: string;
  sub?: SubContent[];
  decorator?: string; // 修饰图
};

const Carousel = defineComponent({
  name: 'Carousel',
  props: {
    items: {
      type: Array as PropType<CarouselItem[]>,
      required: true,
    },
  },
  setup(props) {
    const activeKey = ref(0);
    const changeActiveKey = (key) => {
      activeKey.value = key;
    };
    const activeItem = computed(() => {
      return props.items[unref(activeKey)];
    });
    return {
      activeKey,
      changeActiveKey,
      activeItem,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.tabs} v-show={this.items.length > 1}>
          <div class={styles.wrapper}>
            {this.items.map(({ name }, index) => {
              return (
                <div
                  class={{
                    [styles.tab]: true,
                    [styles.active]: index === this.activeKey,
                  }}
                  onClick={() => this.changeActiveKey(index)}
                  key={`tab-${index}`}
                >
                  <span>{name}</span>
                </div>
              );
            })}
          </div>
        </div>
        <div class={styles.content}>
          <main class={styles.main}>
            <div class={styles.title}>
              <span>{this.activeItem.name}</span>
            </div>
            {this.activeItem.description && (
              <div class={styles.description}>{this.activeItem.description}</div>
            )}
            {this.activeItem.sub &&
              this.activeItem.sub.map((item) => {
                return (
                  <p class={[styles.description, styles.sub]}>
                    <span class={styles.emphasis}>{item.title}</span>
                    {item.content}
                  </p>
                );
              })}
            {this.$slots.default ? <div class={styles.actions}>{this.$slots.default}</div> : null}
          </main>
          <aside class={styles.aside}>
            <img width="580" src={this.activeItem.decorator} alt={this.activeItem.name} />
          </aside>
        </div>
      </div>
    );
  },
});

export default Carousel;
