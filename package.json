{"name": "qcc-benefishield-landing-web", "version": "0.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt generate", "start": "nuxt start", "generate": "nuxt generate", "lint:js": "eslint --ext \".js,.jsx,.ts,.tsx,.vue\" --ignore-path .gitignore .", "lint:style": "stylelint \"**/*.{css,less,html,vue}\" --ignore-path .gitignore", "lint:prettier": "prettier --check .", "lint": "yarn lint:js && yarn lint:style && yarn lint:prettier", "lintfix": "prettier --write --list-different . && yarn lint:js --fix && yarn lint:style --fix", "postinstall": "patch-package"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": "eslint --cache", "*.{css,less,html,vue}": "stylelint", "*.**": "prettier --check --ignore-unknown"}, "dependencies": {"@nuxtjs/axios": "^5.13.6", "@nuxtjs/composition-api": "^0.33.1", "@nuxtjs/pwa": "^3.3.5", "@nuxtjs/sentry": "^7.3.1", "@vueuse/core": "^10.2.1", "@vueuse/nuxt": "^10.2.1", "ant-design-vue": "^1.7.8", "axios": "^0.25.0", "core-js": "^3.31.1", "crypto-js": "^4.2.0", "loadjs": "^4.3.0-rc1", "lodash": "^4.17.21", "nuxt": "^2.17.1", "vue": "^2.7.14", "vue-server-renderer": "^2.7.14", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/eslint-parser": "^7.22.9", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/runtime-corejs3": "^7.22.6", "@commitlint/cli": "^17.6.7", "@nuxt/content": "^1.15.1", "@nuxt/types": "^2.17.2", "@nuxt/typescript-build": "^3.0.1", "@nuxtjs/eslint-config-typescript": "^12.0.0", "@nuxtjs/eslint-module": "^4.1.0", "@nuxtjs/stylelint-module": "^4.2.1", "@nuxtjs/svg": "^0.4.1", "@qcc-ui/commitlint-config": "^1.0.0", "@types/node": "^18.16.18", "@vitejs/plugin-vue2": "^2.2.0", "@vitejs/plugin-vue2-jsx": "^1.1.0", "@vue/test-utils": "^1.3.6", "autoprefixer": "^10.4.15", "defu": "^6.1.2", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-nuxt": "^4.0.0", "eslint-plugin-vue": "^9.15.1", "happy-dom": "^10.5.2", "husky": "^8.0.3", "less": "^4.1.3", "less-loader": "^7.0.0", "lint-staged": "^13.2.3", "mlly": "^1.7.4", "patch-package": "^8.0.0", "postcss": "^8.4.29", "postcss-html": "^1.5.0", "prettier": "^2.7.1", "stylelint": "^15.10.2", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^28.0.0", "typescript": "^5.1.6", "vue-jsx-hot-loader": "^1.4.1", "vue-router": "3.6.5", "webpack": "^4.46.0"}, "resolutions": {"mlly": "1.7.4"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}