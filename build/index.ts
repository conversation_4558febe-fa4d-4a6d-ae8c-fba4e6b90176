import type { NuxtConfig, NuxtOptions } from '@nuxt/types';
import { type BuildMode, withBuildMode, withDefaultConfig } from './core';

type BuildConfigParams = {
  mode: BuildMode;
  port?: number;
} & Partial<
  Pick<
    NuxtOptions,
    | 'axios'
    | 'proxy'
    | 'publicRuntimeConfig'
    | 'privateRuntimeConfig'
    | 'head'
    | 'css'
    | 'plugins'
    | 'sentry'
    | 'publicPath'
    | 'env'
  >
>;

export function defineBuildConfig({
  mode = 'spa',
  port = 8080,
  head,
  css,
  plugins,
  proxy,
  sentry,
  axios,
  publicRuntimeConfig,
  privateRuntimeConfig,
  publicPath,
  env,
}: BuildConfigParams): NuxtConfig {
  return {
    ...withBuildMode(mode),
    ...withDefaultConfig(process.env.NODE_ENV, publicPath),
    head,
    css,
    plugins,
    sentry,
    axios,
    publicRuntimeConfig,
    privateRuntimeConfig,
    proxy,
    env,
    server: {
      port,
      host: '0.0.0.0',
      timing: false,
    },
  };
}
