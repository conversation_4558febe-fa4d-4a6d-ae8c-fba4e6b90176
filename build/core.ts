import { defu } from 'defu';
import type { NuxtConfig, NuxtOptions } from '@nuxt/types';
// import { $content } from '@nuxt/content';
export type BuildMode = 'spa' | 'ssg' | 'ssr';

export function withBuildMode(mode: BuildMode = 'ssr'): Pick<NuxtOptions, 'target' | 'ssr'> {
  switch (mode) {
    case 'spa':
      return {
        target: 'static',
        ssr: false,
      };

    case 'ssg':
      return {
        target: 'static',
        ssr: true,
      };

    case 'ssr':
    default:
      return {
        target: 'server',
        ssr: true,
      };
  }
}

export function withDefaultConfig(env = 'development', publicPath?: string): NuxtConfig {
  const isProd = env === 'production';

  // Default common config
  const defaultConfig: NuxtConfig = {
    modules: [
      // '@nuxtjs/axios',
      // '@nuxtjs/sentry',
      // '@nuxt/content',
      // // 'cookie-universal-nuxt', // 透传 Cookie
    ],
    buildModules: [
      '@nuxt/typescript-build',
      '@nuxtjs/composition-api/module',
      '@vueuse/nuxt',
      '@nuxtjs/svg', // SVG (optional)
    ],
    srcDir: './src/',
    loading: false,
    loadingIndicator: false,
    components: false,
    telemetry: false,
    generate: {
      exclude: [
        /^\/.+\/widgets\/*/, // 忽略 widgets 目录
        /^\/.+\/config\/*/, // 忽略 配置 目录
      ],
      // fallback: '404.html',
      fallback: false,
      nojekyll: false,
      // async routes(callback) {
      //   const manuals = await $content('manuals').fetch<any>()
      //   const routes = manuals.map(data => '/portal/document/guide-manual/' + data.slug)
      //   callback(null, routes)
      // }
    },
    // typescript: {
    //   loaders: {
    //     ts: {
    //       projectReferences: true,
    //     },
    //     tsx: {
    //       projectReferences: true,
    //     },
    //   },
    // },

    svg: {
      fileLoader: ({ isDev }) => ({
        name: isDev ? '[path][name].[ext]' : 'assets/images/[name].[contenthash:7].[ext]',
      }),
    },

    hooks: {
      generate: {
        page(page) {
          // 移除 `data-n-head="ssr"` 属性（解决百度搜索资源平台验证问题）
          page.html = page.html
            .replace(/\s?data-n-head="ssr"/gi, '')
            .replace(/\s?data-n-head-ssr/gi, '');
        }
      }
    },

    build: {
      publicPath,
      corejs: 3,
      postcss: false,
      hardSource: true,
      filenames: {
        app: ({ isDev, isModern }) => isDev ? `[name]${isModern ? '.modern' : ''}.js` : `[contenthash:7]${isModern ? '.modern' : ''}.js`,
        chunk: ({ isDev, isModern }) => isDev ? `[name]${isModern ? '.modern' : ''}.js` : `[contenthash:7]${isModern ? '.modern' : ''}.js`,
        css: ({ isDev }) => (isDev ? '[name].css' : 'assets/styles/[contenthash:7].css'),
        img: ({ isDev }) => (isDev ? '[path][name].[ext]' : 'assets/images/[name].[contenthash:7].[ext]'),
        font: ({ isDev }) => (isDev ? '[path][name].[ext]' : 'assets/fonts/[name].[contenthash:7].[ext]'),
        video: ({ isDev }) => (isDev ? '[path][name].[ext]' : 'assets/videos/[name].[contenthash:7].[ext]'),
      },
      loaders: {
        css: {
          modules: {
            localIdentName: '[folder]__[local]--[hash:base64:5]',
            auto: true,
            compileType: 'module',
            exportLocalsConvention: 'camelCaseOnly',
          },
        },
        less: {
          lessOptions: {
            javascriptEnabled: true,
            math: 'always',
          },
        } as Less.Options,
      },
      babel: {
        presets({ envName }) {
          const envTargets = {
            client: { browsers: ['last 2 versions'], ie: 11 },
            server: { node: 'current' },
          };
          return [
            [
              '@nuxt/babel-preset-app', // https://github.com/nuxt/nuxt/tree/2.x/packages/babel-preset-app#options
              {
                targets: envTargets[envName],
                corejs: { version: 3 },
                jsx: {
                  functional: true,
                  compositionAPI: true,
                },
              },
            ],
          ];
        },
      },
      extend(config, { isDev }) {
        if (isDev) {
          config?.module?.rules.push({
            test: /\.(t|j)sx$/i,
            use: ['vue-jsx-hot-loader'],
          });
        }
      },
    },
  };

  // Default build config
  const defaultBuildConfig: NuxtConfig = {
    build: {
      parallel: true, // 构建时关闭 parallel, 否则会影响 `extractCSS`
      extractCSS: false,
    },
  };

  // Prod build config
  const prodBuildConfig: NuxtConfig = {
    build: {
      parallel: false, // 构建时关闭 parallel, 否则会影响 `extractCSS`
      extractCSS: {
        ignoreOrder: true,
      },
      loaders: {
        css: {
          modules: {
            localIdentName: '[hash:base64:8]',
          },
        },
      },
      optimization: {
        // https://webpack.js.org/plugins/split-chunks-plugin/
        splitChunks: {
          chunks: 'async',
          minSize: 20000,
          // NOTE: 限制分片大小
          // minSize:10000,
          // maxSize:250000,

          // 支持样式分片打包
          cacheGroups: {
            styles: {
              name: 'styles',
              test: /\.(css|less)$/,
              chunks: 'all',
              enforce: true,
            },
          },
        },
        // minimize: true,
      },
    },
  };

  const config = defu(isProd ? prodBuildConfig : {}, defaultBuildConfig, defaultConfig);
  return config;
}
